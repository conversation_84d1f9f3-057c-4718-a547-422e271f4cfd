import {
    type editor as Monaco<PERSON><PERSON><PERSON>,
    EditorScopedLayoutService,
    IAccessibilityService,
    IAccessibilitySignalService,
    IClipboardService,
    ICodeEditorService,
    ICommandService,
    IConfigurationService,
    IContextKeyService,
    IContextMenuService,
    IContextViewService,
    IEditorProgressService,
    IEditorWorkerService,
    IHoverService,
    IInstantiationService,
    IKeybindingService,
    ILanguageConfigurationService,
    ILanguageFeaturesService,
    ILanguageService,
    ILayoutService,
    IModelService,
    INotificationService,
    IOpenerService,
    IQuickInputService,
    IStandaloneThemeService,
    OpenerService,
    QuickInputService,
    ServiceCollection,
    StandaloneDiffEditor,
    StandaloneEditor,
    StandaloneServices,
    StaticServices,
} from 'mo/monaco';
import { inject, injectable } from 'tsyringe';

import { ColorThemeService } from './colorTheme';

type IEditorOverrideServices = MonacoEditor.IEditorOverrideServices;

/**
 * Custom QuickInputService that doesn't require a focused editor to work.
 * This fixes the issue where QuickInputService in Monaco Editor 0.52.2
 * requires a focused editor, breaking keyboard shortcuts when no editor is focused.
 */
class MoleculeQuickInputService {
    private fallbackService: any;
    private editorScopedServices = new Map<any, any>();

    constructor(
        private instantiationService: any,
        private codeEditorService: any,
        private container: HTMLElement
    ) {
        // Create a fallback service that uses the container as layout service
        const layoutService = new EditorScopedLayoutService(
            this.container,
            this.codeEditorService
        );

        this.fallbackService = this.instantiationService.createInstance(
            QuickInputService,
            this.instantiationService,
            undefined, // contextKeyService - will be resolved by DI
            undefined, // themeService - will be resolved by DI
            layoutService,
            undefined  // configurationService - will be resolved by DI
        );
    }

    get activeService() {
        const editor = this.codeEditorService.getFocusedCodeEditor();
        if (editor) {
            // If we have a focused editor, use editor-scoped service
            let editorService = this.editorScopedServices.get(editor);
            if (!editorService) {
                const layoutService = new EditorScopedLayoutService(
                    editor.getContainerDomNode(),
                    this.codeEditorService
                );
                editorService = this.instantiationService.createInstance(
                    QuickInputService,
                    this.instantiationService,
                    undefined, // contextKeyService
                    undefined, // themeService
                    layoutService,
                    undefined  // configurationService
                );
                this.editorScopedServices.set(editor, editorService);

                // Clean up when editor is disposed
                editor.onDidDispose(() => {
                    editorService?.dispose();
                    this.editorScopedServices.delete(editor);
                });
            }
            return editorService;
        }

        // Fallback to container-scoped service when no editor is focused
        return this.fallbackService;
    }

    get currentQuickInput() {
        return this.activeService.currentQuickInput;
    }

    get quickAccess() {
        return this.activeService.quickAccess;
    }

    pick(picks: any, options?: any, token?: any) {
        return this.activeService.pick(picks, options, token);
    }

    createQuickPick(options = { useSeparators: false }) {
        return this.activeService.createQuickPick(options);
    }

    createInputBox() {
        return this.activeService.createInputBox();
    }

    dispose() {
        this.fallbackService?.dispose();
        this.editorScopedServices.forEach(service => service.dispose());
        this.editorScopedServices.clear();
    }
}

@injectable()
export class MonacoService {
    private _services: ServiceCollection;
    private _container!: HTMLElement | null;

    constructor(@inject('colorTheme') private colorTheme: ColorThemeService) {}

    public initWorkspace(container: HTMLElement) {
        this._container = container;
        this._services = this.createStandaloneServices();
    }

    get container() {
        return this._container;
    }

    get services() {
        return this._services;
    }

    get commandService() {
        return this.services.get(ICommandService);
    }

    get QuickInputService(): IQuickInputService {
        return this.services.get(IQuickInputService);
    }

    private mergeEditorServices(overrides?: IEditorOverrideServices) {
        if (overrides) {
            const services = this.services;
            for (const serviceId in overrides) {
                if (serviceId) {
                    const service = services.get(serviceId);
                    if (service && overrides[serviceId]) {
                        services.set(serviceId, overrides[serviceId]);
                    }
                }
            }
        }
    }

    public create(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneCodeEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneEditor = new StandaloneEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(ICodeEditorService),
            services.get(ICommandService),
            services.get(IContextKeyService),
            services.get(IHoverService),
            services.get(IKeybindingService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IAccessibilityService),
            services.get(IModelService),
            services.get(ILanguageService),
            services.get(ILanguageConfigurationService),
            services.get(ILanguageFeaturesService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneEditor;
    }

    public createDiffEditor(
        domElement: HTMLElement,
        options?: MonacoEditor.IStandaloneDiffEditorConstructionOptions,
        overrides?: IEditorOverrideServices
    ): MonacoEditor.IStandaloneDiffEditor {
        const services = this.services;

        this.mergeEditorServices(overrides);

        const standaloneDiffEditor = new StandaloneDiffEditor(
            domElement,
            options,
            services.get(IInstantiationService),
            services.get(IContextKeyService),
            services.get(ICodeEditorService),
            services.get(IStandaloneThemeService),
            services.get(INotificationService),
            services.get(IConfigurationService),
            services.get(IContextMenuService),
            services.get(IEditorProgressService),
            services.get(IClipboardService),
            services.get(IAccessibilitySignalService)
        );

        // Should be called after the editor is created
        this.colorTheme.setCurrent(this.colorTheme.getCurrent());

        return standaloneDiffEditor;
    }

    // When Application will unmount, call it
    public dispose() {}

    private createStandaloneServices(): ServiceCollection {
        const instantiationService = StandaloneServices.initialize({});
        const services = new ServiceCollection();
        const serviceIds = [
            IInstantiationService,
            ICodeEditorService,
            ICommandService,
            IConfigurationService,
            IContextKeyService,
            IKeybindingService,
            IContextViewService,
            IStandaloneThemeService,
            INotificationService,
            IAccessibilityService,
            IAccessibilitySignalService,
            IModelService,
            ILanguageService,
            ILanguageConfigurationService,
            ILanguageFeaturesService,
            IHoverService,
            IEditorWorkerService,
            IContextMenuService,
            IEditorProgressService,
            IClipboardService,
        ];

        serviceIds.forEach(serviceId => {
            const service = StandaloneServices.get(serviceId);
            if (service) {
                services.set(serviceId, service);
            }
        });

        if (!services.get(IOpenerService)) {
            services.set(
                IOpenerService,
                new OpenerService(services.get(ICodeEditorService), services.get(ICommandService))
            );
        }

        // Create custom QuickInputService that doesn't require focused editor
        const quickInputService = new MoleculeQuickInputService(
            instantiationService,
            services.get(ICodeEditorService),
            this.container!
        );

        const layoutService = new EditorScopedLayoutService(
            this.container,
            StaticServices.codeEditorService.get(ICodeEditorService)
        );

        // Override layoutService
        services.set(ILayoutService, layoutService);

        // Override quickPickService with our custom implementation
        services.set(IQuickInputService, quickInputService as any);

        // Override dispose for prevent disposed by instance
        this.dispose = services.dispose;
        services.dispose = () => {};
        return services;
    }
}
